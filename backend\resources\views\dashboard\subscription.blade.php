<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Subscription') }}
        </h2>
    </x-slot>
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Subscription</h1>
                        <p class="text-gray-600">Manage your GOC Agent subscription and billing</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Current Subscription -->
        @if($currentSubscription)
        <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Current Subscription</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider">Plan</h4>
                        <div class="mt-2 flex items-center">
                            <span class="text-2xl font-bold text-gray-900">{{ $currentSubscription->subscriptionPlan->name }}</span>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $currentSubscription->isActive() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ ucfirst($currentSubscription->status) }}
                            </span>
                        </div>
                        <p class="mt-1 text-sm text-gray-600">{{ $currentSubscription->subscriptionPlan->description }}</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider">Billing</h4>
                        <div class="mt-2">
                            <span class="text-2xl font-bold text-gray-900">
                                ${{ number_format($currentSubscription->subscriptionPlan->price, 0) }}
                            </span>
                            <span class="text-gray-600">/{{ $currentSubscription->billing_cycle }}</span>
                        </div>
                        @if($currentSubscription->current_period_end)
                        <p class="mt-1 text-sm text-gray-600">
                            {{ $currentSubscription->status === 'canceled' ? 'Ends' : 'Renews' }} on {{ $currentSubscription->current_period_end->format('M j, Y') }}
                        </p>
                        @endif
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider">Usage</h4>
                        <div class="mt-2">
                            <div class="text-sm text-gray-900">
                                <div>Daily: {{ number_format($currentSubscription->api_requests_used_today) }} / {{ $currentSubscription->subscriptionPlan->api_requests_daily > 0 ? number_format($currentSubscription->subscriptionPlan->api_requests_daily) : '∞' }}</div>
                                <div>Monthly: {{ number_format($currentSubscription->api_requests_used_month) }} / {{ $currentSubscription->subscriptionPlan->api_requests_monthly > 0 ? number_format($currentSubscription->subscriptionPlan->api_requests_monthly) : '∞' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                @if($currentSubscription->isActive())
                <div class="mt-6 flex items-center space-x-4">
                    @if(!$currentSubscription->subscriptionPlan->isFree())
                    <form method="POST" action="{{ route('subscription.cancel-subscription') }}">
                        @csrf
                        <button type="submit" onclick="return confirm('Are you sure you want to cancel your subscription?')" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Cancel Subscription
                        </button>
                    </form>
                    @endif
                </div>
                @elseif($currentSubscription->status === 'canceled')
                <div class="mt-6">
                    <form method="POST" action="{{ route('subscription.resume') }}">
                        @csrf
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Resume Subscription
                        </button>
                    </form>
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Available Plans -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    {{ $currentSubscription ? 'Upgrade or Change Plan' : 'Choose Your Plan' }}
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($plans as $plan)
                    <div class="border border-gray-200 rounded-lg p-6 {{ $plan->is_popular ? 'ring-2 ring-indigo-500' : '' }} {{ $currentSubscription && $currentSubscription->subscription_plan_id === $plan->id ? 'bg-gray-50' : '' }}">
                        @if($plan->is_popular)
                        <div class="flex justify-center">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 mb-4">
                                Most Popular
                            </span>
                        </div>
                        @endif

                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-900">{{ $plan->name }}</h3>
                            <p class="mt-2 text-sm text-gray-600">{{ $plan->description }}</p>
                            
                            <div class="mt-4">
                                <span class="text-4xl font-bold text-gray-900">${{ number_format($plan->price, 0) }}</span>
                                <span class="text-gray-600">/month</span>
                                @if($plan->yearly_price && $plan->yearly_discount)
                                <div class="text-sm text-green-600 mt-1">
                                    Save {{ $plan->yearly_discount }}% with yearly billing
                                </div>
                                @endif
                            </div>

                            <ul class="mt-6 space-y-3 text-sm">
                                @foreach($plan->features as $feature)
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $feature }}</span>
                                </li>
                                @endforeach
                            </ul>

                            <div class="mt-6">
                                @if($currentSubscription && $currentSubscription->subscription_plan_id === $plan->id && $currentSubscription->isActive())
                                <button disabled class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100 cursor-not-allowed">
                                    Current Plan
                                </button>
                                @else
                                <div x-data="{ billingCycle: 'monthly' }">
                                    @if($plan->yearly_price)
                                    <div class="mb-4">
                                        <div class="flex items-center justify-center space-x-4">
                                            <label class="flex items-center">
                                                <input type="radio" x-model="billingCycle" value="monthly" class="form-radio text-indigo-600">
                                                <span class="ml-2 text-sm">Monthly</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" x-model="billingCycle" value="yearly" class="form-radio text-indigo-600">
                                                <span class="ml-2 text-sm">Yearly</span>
                                            </label>
                                        </div>
                                    </div>
                                    @endif

                                    <form method="POST" action="{{ route('subscription.subscribe', $plan) }}">
                                        @csrf
                                        <input type="hidden" name="billing_cycle" x-model="billingCycle">
                                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors">
                                            {{ $plan->isFree() ? 'Get Started Free' : ($currentSubscription ? 'Switch to ' . $plan->name : 'Subscribe to ' . $plan->name) }}
                                        </button>
                                    </form>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Billing History -->
        @if($currentSubscription && !$currentSubscription->subscriptionPlan->isFree())
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Billing History</h3>
            </div>
            <div class="p-6">
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No billing history</h3>
                    <p class="mt-1 text-sm text-gray-500">Your billing history will appear here once you have invoices.</p>
                </div>
            </div>
        </div>
        @endif

        <!-- FAQ Section -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Frequently Asked Questions</h3>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Can I change my plan at any time?</h4>
                        <p class="mt-1 text-sm text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences.</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">What happens if I exceed my API limits?</h4>
                        <p class="mt-1 text-sm text-gray-600">If you exceed your daily or monthly API limits, your requests will be temporarily throttled. Consider upgrading to a higher plan for increased limits.</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Can I cancel my subscription?</h4>
                        <p class="mt-1 text-sm text-gray-600">Yes, you can cancel your subscription at any time. You'll continue to have access to your current plan until the end of your billing period.</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Do you offer refunds?</h4>
                        <p class="mt-1 text-sm text-gray-600">We offer a 30-day money-back guarantee for all paid plans. Contact our support team if you're not satisfied with the service.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</x-app-layout>
