<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

use Symfony\Polyfill\Intl\Icu\IntlDateFormatter as IntlDateFormatterPolyfill;

/**
 * Stub implementation for the IntlDateFormatter class of the intl extension.
 *
 * <AUTHOR> <<EMAIL>>
 */
class IntlDateFormatter extends IntlDateFormatterPolyfill
{
}
