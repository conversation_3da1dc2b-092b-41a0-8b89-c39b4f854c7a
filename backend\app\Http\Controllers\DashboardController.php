<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    // Middleware is now applied in routes/web.php

    /**
     * Show the dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        $subscription = $user->subscription;

        // Get usage statistics
        $todayUsage = $subscription ? $subscription->api_requests_used_today : 0;
        $monthUsage = $subscription ? $subscription->api_requests_used_month : 0;

        // Get limits
        $dailyLimit = $subscription ? $subscription->subscriptionPlan->api_requests_daily : 0;
        $monthlyLimit = $subscription ? $subscription->subscriptionPlan->api_requests_monthly : 0;

        // Get recent usage logs
        $recentUsage = $user->usageLogs()
            ->latest()
            ->take(10)
            ->get();

        // Get API keys count
        $apiKeysCount = $user->userApiKeys()->where('is_active', true)->count();

        return view('dashboard.index', compact(
            'user',
            'subscription',
            'todayUsage',
            'monthUsage',
            'dailyLimit',
            'monthlyLimit',
            'recentUsage',
            'apiKeysCount'
        ));
    }

    /**
     * Show API keys management.
     */
    public function apiKeys()
    {
        $user = Auth::user();
        $apiKeys = $user->userApiKeys()->latest()->get();

        return view('dashboard.api-keys', compact('apiKeys'));
    }

    /**
     * Show subscription management.
     */
    public function subscription()
    {
        $user = Auth::user();
        $currentSubscription = $user->subscription;
        $plans = \App\Models\SubscriptionPlan::active()->ordered()->get();

        return view('dashboard.subscription', compact('currentSubscription', 'plans'));
    }

    /**
     * Show usage analytics.
     */
    public function usage()
    {
        $user = Auth::user();

        // Get usage data for charts
        $dailyUsage = $user->usageLogs()
            ->selectRaw('DATE(created_at) as date, COUNT(*) as requests')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $endpointUsage = $user->usageLogs()
            ->selectRaw('endpoint, COUNT(*) as requests')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('endpoint')
            ->orderByDesc('requests')
            ->take(10)
            ->get();

        return view('dashboard.usage', compact('dailyUsage', 'endpointUsage'));
    }
}
