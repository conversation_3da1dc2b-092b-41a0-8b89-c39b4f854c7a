<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Subscription')); ?>

        </h2>
     <?php $__env->endSlot(); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Subscription</h1>
                        <p class="text-gray-600">Manage your GOC Agent subscription and billing</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Current Subscription -->
        <?php if($currentSubscription): ?>
        <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Current Subscription</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider">Plan</h4>
                        <div class="mt-2 flex items-center">
                            <span class="text-2xl font-bold text-gray-900"><?php echo e($currentSubscription->subscriptionPlan->name); ?></span>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($currentSubscription->isActive() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                <?php echo e(ucfirst($currentSubscription->status)); ?>

                            </span>
                        </div>
                        <p class="mt-1 text-sm text-gray-600"><?php echo e($currentSubscription->subscriptionPlan->description); ?></p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider">Billing</h4>
                        <div class="mt-2">
                            <span class="text-2xl font-bold text-gray-900">
                                $<?php echo e(number_format($currentSubscription->subscriptionPlan->price, 0)); ?>

                            </span>
                            <span class="text-gray-600">/<?php echo e($currentSubscription->billing_cycle); ?></span>
                        </div>
                        <?php if($currentSubscription->current_period_end): ?>
                        <p class="mt-1 text-sm text-gray-600">
                            <?php echo e($currentSubscription->status === 'canceled' ? 'Ends' : 'Renews'); ?> on <?php echo e($currentSubscription->current_period_end->format('M j, Y')); ?>

                        </p>
                        <?php endif; ?>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider">Usage</h4>
                        <div class="mt-2">
                            <div class="text-sm text-gray-900">
                                <div>Daily: <?php echo e(number_format($currentSubscription->api_requests_used_today)); ?> / <?php echo e($currentSubscription->subscriptionPlan->api_requests_daily > 0 ? number_format($currentSubscription->subscriptionPlan->api_requests_daily) : '∞'); ?></div>
                                <div>Monthly: <?php echo e(number_format($currentSubscription->api_requests_used_month)); ?> / <?php echo e($currentSubscription->subscriptionPlan->api_requests_monthly > 0 ? number_format($currentSubscription->subscriptionPlan->api_requests_monthly) : '∞'); ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if($currentSubscription->isActive()): ?>
                <div class="mt-6 flex items-center space-x-4">
                    <?php if(!$currentSubscription->subscriptionPlan->isFree()): ?>
                    <form method="POST" action="<?php echo e(route('subscription.cancel-subscription')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" onclick="return confirm('Are you sure you want to cancel your subscription?')" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Cancel Subscription
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
                <?php elseif($currentSubscription->status === 'canceled'): ?>
                <div class="mt-6">
                    <form method="POST" action="<?php echo e(route('subscription.resume')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Resume Subscription
                        </button>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Available Plans -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <?php echo e($currentSubscription ? 'Upgrade or Change Plan' : 'Choose Your Plan'); ?>

                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="border border-gray-200 rounded-lg p-6 <?php echo e($plan->is_popular ? 'ring-2 ring-indigo-500' : ''); ?> <?php echo e($currentSubscription && $currentSubscription->subscription_plan_id === $plan->id ? 'bg-gray-50' : ''); ?>">
                        <?php if($plan->is_popular): ?>
                        <div class="flex justify-center">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 mb-4">
                                Most Popular
                            </span>
                        </div>
                        <?php endif; ?>

                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-900"><?php echo e($plan->name); ?></h3>
                            <p class="mt-2 text-sm text-gray-600"><?php echo e($plan->description); ?></p>
                            
                            <div class="mt-4">
                                <span class="text-4xl font-bold text-gray-900">$<?php echo e(number_format($plan->price, 0)); ?></span>
                                <span class="text-gray-600">/month</span>
                                <?php if($plan->yearly_price && $plan->yearly_discount): ?>
                                <div class="text-sm text-green-600 mt-1">
                                    Save <?php echo e($plan->yearly_discount); ?>% with yearly billing
                                </div>
                                <?php endif; ?>
                            </div>

                            <ul class="mt-6 space-y-3 text-sm">
                                <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700"><?php echo e($feature); ?></span>
                                </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>

                            <div class="mt-6">
                                <?php if($currentSubscription && $currentSubscription->subscription_plan_id === $plan->id && $currentSubscription->isActive()): ?>
                                <button disabled class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100 cursor-not-allowed">
                                    Current Plan
                                </button>
                                <?php else: ?>
                                <div x-data="{ billingCycle: 'monthly' }">
                                    <?php if($plan->yearly_price): ?>
                                    <div class="mb-4">
                                        <div class="flex items-center justify-center space-x-4">
                                            <label class="flex items-center">
                                                <input type="radio" x-model="billingCycle" value="monthly" class="form-radio text-indigo-600">
                                                <span class="ml-2 text-sm">Monthly</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" x-model="billingCycle" value="yearly" class="form-radio text-indigo-600">
                                                <span class="ml-2 text-sm">Yearly</span>
                                            </label>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <form method="POST" action="<?php echo e(route('subscription.subscribe', $plan)); ?>">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="billing_cycle" x-model="billingCycle">
                                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors">
                                            <?php echo e($plan->isFree() ? 'Get Started Free' : ($currentSubscription ? 'Switch to ' . $plan->name : 'Subscribe to ' . $plan->name)); ?>

                                        </button>
                                    </form>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Billing History -->
        <?php if($currentSubscription && !$currentSubscription->subscriptionPlan->isFree()): ?>
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Billing History</h3>
            </div>
            <div class="p-6">
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No billing history</h3>
                    <p class="mt-1 text-sm text-gray-500">Your billing history will appear here once you have invoices.</p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- FAQ Section -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Frequently Asked Questions</h3>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Can I change my plan at any time?</h4>
                        <p class="mt-1 text-sm text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences.</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">What happens if I exceed my API limits?</h4>
                        <p class="mt-1 text-sm text-gray-600">If you exceed your daily or monthly API limits, your requests will be temporarily throttled. Consider upgrading to a higher plan for increased limits.</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Can I cancel my subscription?</h4>
                        <p class="mt-1 text-sm text-gray-600">Yes, you can cancel your subscription at any time. You'll continue to have access to your current plan until the end of your billing period.</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Do you offer refunds?</h4>
                        <p class="mt-1 text-sm text-gray-600">We offer a 30-day money-back guarantee for all paid plans. Contact our support team if you're not satisfied with the service.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\goc-agent\backend\resources\views/dashboard/subscription.blade.php ENDPATH**/ ?>