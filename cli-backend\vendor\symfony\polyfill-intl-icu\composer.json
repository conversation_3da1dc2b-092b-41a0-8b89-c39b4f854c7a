{"name": "symfony/polyfill-intl-icu", "type": "library", "description": "Symfony polyfill for intl's ICU-related data and classes", "keywords": ["polyfill", "shim", "compatibility", "portable", "intl", "icu"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.2"}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "minimum-stability": "dev", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}}