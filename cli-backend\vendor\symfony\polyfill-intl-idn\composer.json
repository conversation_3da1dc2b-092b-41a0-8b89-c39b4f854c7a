{"name": "symfony/polyfill-intl-idn", "type": "library", "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "keywords": ["polyfill", "shim", "compatibility", "portable", "intl", "idn"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "suggest": {"ext-intl": "For best performance"}, "minimum-stability": "dev", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}}