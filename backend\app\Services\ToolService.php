<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process as SymfonyProcess;
use Illuminate\Support\Facades\Cache;

class ToolService
{
    protected array $runningProcesses = [];
    protected WebResearchService $webResearch;
    protected MemoryService $memoryService;
    protected MermaidService $mermaidService;
    protected int $nextProcessId = 1;

    public function __construct(WebResearchService $webResearch, MemoryService $memoryService, MermaidService $mermaidService)
    {
        $this->webResearch = $webResearch;
        $this->memoryService = $memoryService;
        $this->mermaidService = $mermaidService;
        $this->loadRunningProcesses();
    }

    /**
     * Execute a tool with given parameters.
     */
    public function executeTool(string $tool, array $parameters, $user = null): array
    {
        try {
            return match ($tool) {
                'view' => $this->viewTool($parameters),
                'str-replace-editor' => $this->strReplaceEditorTool($parameters),
                'save-file' => $this->saveFileTool($parameters),
                'remove-files' => $this->removeFilesTool($parameters),
                'launch-process' => $this->launchProcessTool($parameters),
                'read-process' => $this->readProcessTool($parameters),
                'write-process' => $this->writeProcessTool($parameters),
                'kill-process' => $this->killProcessTool($parameters),
                'list-processes' => $this->listProcessesTool($parameters),
                'diagnostics' => $this->diagnosticsTool($parameters),
                'remember' => $this->rememberTool($parameters, $user),
                'render-mermaid' => $this->renderMermaidTool($parameters),
                'open-browser' => $this->openBrowserTool($parameters),
                'read-terminal' => $this->readTerminalTool($parameters),
                'web-search' => $this->webSearchTool($parameters),
                'web-fetch' => $this->webFetchTool($parameters),
                default => [
                    'success' => false,
                    'error' => "Unknown tool: {$tool}",
                    'data' => null,
                ],
            };
        } catch (\Exception $e) {
            Log::error("Tool execution error", [
                'tool' => $tool,
                'parameters' => $parameters,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * View file or directory contents.
     */
    protected function viewTool(array $parameters): array
    {
        $path = $parameters['path'] ?? '';
        $type = $parameters['type'] ?? 'auto';
        $searchQuery = $parameters['search_query_regex'] ?? null;
        $viewRange = $parameters['view_range'] ?? null;
        $caseSensitive = $parameters['case_sensitive'] ?? false;
        $contextLinesBefore = $parameters['context_lines_before'] ?? 5;
        $contextLinesAfter = $parameters['context_lines_after'] ?? 5;

        if (!$path) {
            return ['success' => false, 'error' => 'Path parameter is required'];
        }

        $fullPath = $this->resolvePath($path);

        if (!File::exists($fullPath)) {
            return ['success' => false, 'error' => "Path does not exist: {$path}"];
        }

        if (File::isDirectory($fullPath)) {
            return $this->viewDirectory($fullPath);
        } else {
            return $this->viewFile($fullPath, $searchQuery, $viewRange, $caseSensitive, $contextLinesBefore, $contextLinesAfter);
        }
    }

    /**
     * View directory contents.
     */
    protected function viewDirectory(string $path): array
    {
        $items = [];
        $files = File::glob($path . '/*');
        
        foreach ($files as $file) {
            $relativePath = str_replace(base_path() . '/', '', $file);
            $items[] = [
                'name' => basename($file),
                'path' => $relativePath,
                'type' => File::isDirectory($file) ? 'directory' : 'file',
                'size' => File::isFile($file) ? File::size($file) : null,
                'modified' => File::lastModified($file),
            ];
        }

        return [
            'success' => true,
            'data' => [
                'type' => 'directory',
                'path' => $path,
                'items' => $items,
                'total_items' => count($items),
            ],
        ];
    }

    /**
     * View file contents.
     */
    protected function viewFile(string $path, ?string $searchQuery, ?array $viewRange, bool $caseSensitive, int $contextBefore, int $contextAfter): array
    {
        $content = File::get($path);
        $lines = explode("\n", $content);
        $totalLines = count($lines);

        // Apply view range if specified
        if ($viewRange) {
            $start = max(1, $viewRange[0]) - 1; // Convert to 0-based index
            $end = $viewRange[1] === -1 ? $totalLines - 1 : min($totalLines - 1, $viewRange[1] - 1);
            $lines = array_slice($lines, $start, $end - $start + 1, true);
        }

        // Apply regex search if specified
        if ($searchQuery) {
            $flags = $caseSensitive ? 0 : PREG_CASE_INSENSITIVE;
            $matchedLines = [];
            
            foreach ($lines as $lineNumber => $line) {
                if (preg_match("/{$searchQuery}/", $line, $matches, $flags)) {
                    // Add context lines
                    $startContext = max(0, $lineNumber - $contextBefore);
                    $endContext = min(count($lines) - 1, $lineNumber + $contextAfter);
                    
                    for ($i = $startContext; $i <= $endContext; $i++) {
                        if (isset($lines[$i])) {
                            $matchedLines[$i] = $lines[$i];
                        }
                    }
                }
            }
            $lines = $matchedLines;
        }

        // Format output with line numbers
        $formattedLines = [];
        foreach ($lines as $lineNumber => $line) {
            $formattedLines[] = sprintf("%6d\t%s", $lineNumber + 1, $line);
        }

        return [
            'success' => true,
            'data' => [
                'type' => 'file',
                'path' => $path,
                'content' => implode("\n", $formattedLines),
                'total_lines' => $totalLines,
                'displayed_lines' => count($lines),
                'search_query' => $searchQuery,
                'view_range' => $viewRange,
            ],
        ];
    }

    /**
     * String replace editor tool.
     */
    protected function strReplaceEditorTool(array $parameters): array
    {
        $path = $parameters['path'] ?? '';
        $oldStr = $parameters['old_str_1'] ?? '';
        $newStr = $parameters['new_str_1'] ?? '';
        $startLine = $parameters['old_str_start_line_number_1'] ?? null;
        $endLine = $parameters['old_str_end_line_number_1'] ?? null;

        if (!$path || !isset($parameters['old_str_1'])) {
            return ['success' => false, 'error' => 'Path and old_str_1 parameters are required'];
        }

        $fullPath = $this->resolvePath($path);

        if (!File::exists($fullPath)) {
            return ['success' => false, 'error' => "File does not exist: {$path}"];
        }

        $content = File::get($fullPath);
        $lines = explode("\n", $content);

        // If line numbers are specified, validate the old string matches
        if ($startLine && $endLine) {
            $startIndex = $startLine - 1; // Convert to 0-based
            $endIndex = $endLine - 1;
            
            if ($startIndex < 0 || $endIndex >= count($lines) || $startIndex > $endIndex) {
                return ['success' => false, 'error' => 'Invalid line range specified'];
            }

            $extractedLines = array_slice($lines, $startIndex, $endIndex - $startIndex + 1);
            $extractedStr = implode("\n", $extractedLines);

            if ($extractedStr !== $oldStr) {
                return ['success' => false, 'error' => 'Old string does not match content at specified line range'];
            }

            // Perform replacement
            $newLines = explode("\n", $newStr);
            array_splice($lines, $startIndex, $endIndex - $startIndex + 1, $newLines);
        } else {
            // Simple string replacement
            $newContent = str_replace($oldStr, $newStr, $content);
            if ($newContent === $content) {
                return ['success' => false, 'error' => 'Old string not found in file'];
            }
            $lines = explode("\n", $newContent);
        }

        // Write back to file
        $newContent = implode("\n", $lines);
        File::put($fullPath, $newContent);

        return [
            'success' => true,
            'data' => [
                'path' => $path,
                'lines_changed' => $endLine ? ($endLine - $startLine + 1) : substr_count($oldStr, "\n") + 1,
                'new_total_lines' => count($lines),
                'message' => 'File updated successfully',
            ],
        ];
    }

    /**
     * Save file tool.
     */
    protected function saveFileTool(array $parameters): array
    {
        $path = $parameters['path'] ?? '';
        $content = $parameters['file_content'] ?? '';
        $addNewline = $parameters['add_last_line_newline'] ?? true;

        if (!$path) {
            return ['success' => false, 'error' => 'Path parameter is required'];
        }

        $fullPath = $this->resolvePath($path);

        // Create directory if it doesn't exist
        $directory = dirname($fullPath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        // Add newline at end if requested
        if ($addNewline && !empty($content) && !str_ends_with($content, "\n")) {
            $content .= "\n";
        }

        File::put($fullPath, $content);

        return [
            'success' => true,
            'data' => [
                'path' => $path,
                'size' => strlen($content),
                'lines' => substr_count($content, "\n") + 1,
                'message' => 'File saved successfully',
            ],
        ];
    }

    /**
     * Remove files tool.
     */
    protected function removeFilesTool(array $parameters): array
    {
        $filePaths = $parameters['file_paths'] ?? [];

        if (empty($filePaths)) {
            return ['success' => false, 'error' => 'file_paths parameter is required'];
        }

        $removed = [];
        $errors = [];

        foreach ($filePaths as $path) {
            $fullPath = $this->resolvePath($path);
            
            if (!File::exists($fullPath)) {
                $errors[] = "File does not exist: {$path}";
                continue;
            }

            try {
                if (File::isDirectory($fullPath)) {
                    File::deleteDirectory($fullPath);
                } else {
                    File::delete($fullPath);
                }
                $removed[] = $path;
            } catch (\Exception $e) {
                $errors[] = "Failed to remove {$path}: " . $e->getMessage();
            }
        }

        return [
            'success' => empty($errors),
            'data' => [
                'removed' => $removed,
                'errors' => $errors,
                'total_removed' => count($removed),
            ],
        ];
    }

    /**
     * Resolve relative path to absolute path.
     */
    protected function resolvePath(string $path): string
    {
        // If path is already absolute, return as is
        if (str_starts_with($path, '/') || preg_match('/^[A-Z]:/i', $path)) {
            return $path;
        }

        // Resolve relative to project root
        return base_path($path);
    }

    /**
     * Launch a new process.
     */
    protected function launchProcessTool(array $parameters): array
    {
        $command = $parameters['command'] ?? '';
        $cwd = $parameters['cwd'] ?? base_path();
        $wait = $parameters['wait'] ?? false;
        $maxWaitSeconds = $parameters['max_wait_seconds'] ?? 60;

        if (!$command) {
            return ['success' => false, 'error' => 'Command parameter is required'];
        }

        try {
            $processId = $this->nextProcessId++;

            if ($wait) {
                // Synchronous execution with timeout
                $process = Process::timeout($maxWaitSeconds)->path($cwd)->run($command);

                $result = [
                    'success' => $process->successful(),
                    'terminal_id' => $processId,
                    'output' => $process->output(),
                    'error_output' => $process->errorOutput(),
                    'exit_code' => $process->exitCode(),
                    'status' => $process->successful() ? 'completed' : 'failed',
                ];

                return $result;
            } else {
                // Asynchronous execution
                $process = Process::path($cwd)->start($command);

                $this->runningProcesses[$processId] = [
                    'process' => $process,
                    'command' => $command,
                    'cwd' => $cwd,
                    'started_at' => now(),
                    'status' => 'running',
                ];

                $this->saveRunningProcesses();

                return [
                    'success' => true,
                    'terminal_id' => $processId,
                    'status' => 'running',
                    'message' => "Process launched with terminal ID {$processId}",
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to launch process: {$e->getMessage()}",
            ];
        }
    }

    protected function readProcessTool(array $parameters): array
    {
        $terminalId = $parameters['terminal_id'] ?? null;
        $wait = $parameters['wait'] ?? false;
        $maxWaitSeconds = $parameters['max_wait_seconds'] ?? 60;

        if (!$terminalId) {
            return ['success' => false, 'error' => 'terminal_id parameter is required'];
        }

        if (!isset($this->runningProcesses[$terminalId])) {
            return ['success' => false, 'error' => "Process with terminal ID {$terminalId} not found"];
        }

        try {
            $processInfo = $this->runningProcesses[$terminalId];
            $process = $processInfo['process'];

            if ($wait && $process->running()) {
                // Wait for process to complete or timeout
                $startTime = time();
                while ($process->running() && (time() - $startTime) < $maxWaitSeconds) {
                    usleep(100000); // 100ms
                }
            }

            $output = $process->output();
            $errorOutput = $process->errorOutput();
            $isRunning = $process->running();
            $exitCode = $isRunning ? null : $process->exitCode();

            // Update process status
            if (!$isRunning) {
                $this->runningProcesses[$terminalId]['status'] = $process->successful() ? 'completed' : 'failed';
                $this->runningProcesses[$terminalId]['exit_code'] = $exitCode;
                $this->runningProcesses[$terminalId]['completed_at'] = now();
                $this->saveRunningProcesses();
            }

            return [
                'success' => true,
                'terminal_id' => $terminalId,
                'output' => $output,
                'error_output' => $errorOutput,
                'status' => $isRunning ? 'running' : ($process->successful() ? 'completed' : 'failed'),
                'exit_code' => $exitCode,
                'is_running' => $isRunning,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to read process: {$e->getMessage()}",
            ];
        }
    }

    protected function writeProcessTool(array $parameters): array
    {
        $terminalId = $parameters['terminal_id'] ?? null;
        $inputText = $parameters['input_text'] ?? '';

        if (!$terminalId) {
            return ['success' => false, 'error' => 'terminal_id parameter is required'];
        }

        if (!isset($this->runningProcesses[$terminalId])) {
            return ['success' => false, 'error' => "Process with terminal ID {$terminalId} not found"];
        }

        try {
            $processInfo = $this->runningProcesses[$terminalId];
            $process = $processInfo['process'];

            if (!$process->running()) {
                return ['success' => false, 'error' => 'Process is not running'];
            }

            // Write to process stdin
            $process->input($inputText);

            return [
                'success' => true,
                'terminal_id' => $terminalId,
                'message' => 'Input written to process',
                'bytes_written' => strlen($inputText),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to write to process: {$e->getMessage()}",
            ];
        }
    }

    protected function killProcessTool(array $parameters): array
    {
        $terminalId = $parameters['terminal_id'] ?? null;

        if (!$terminalId) {
            return ['success' => false, 'error' => 'terminal_id parameter is required'];
        }

        if (!isset($this->runningProcesses[$terminalId])) {
            return ['success' => false, 'error' => "Process with terminal ID {$terminalId} not found"];
        }

        try {
            $processInfo = $this->runningProcesses[$terminalId];
            $process = $processInfo['process'];

            if (!$process->running()) {
                return [
                    'success' => true,
                    'terminal_id' => $terminalId,
                    'message' => 'Process was already terminated',
                    'status' => 'already_terminated',
                ];
            }

            // Stop the process
            $process->stop();

            // Update process status
            $this->runningProcesses[$terminalId]['status'] = 'killed';
            $this->runningProcesses[$terminalId]['killed_at'] = now();
            $this->saveRunningProcesses();

            return [
                'success' => true,
                'terminal_id' => $terminalId,
                'message' => 'Process terminated successfully',
                'status' => 'killed',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to kill process: {$e->getMessage()}",
            ];
        }
    }

    protected function listProcessesTool(array $parameters): array
    {
        try {
            $processes = [];

            foreach ($this->runningProcesses as $terminalId => $processInfo) {
                $process = $processInfo['process'];
                $isRunning = $process->running();

                $processes[] = [
                    'terminal_id' => $terminalId,
                    'command' => $processInfo['command'],
                    'cwd' => $processInfo['cwd'],
                    'status' => $isRunning ? 'running' : $processInfo['status'],
                    'started_at' => $processInfo['started_at']->toISOString(),
                    'is_running' => $isRunning,
                    'exit_code' => $processInfo['exit_code'] ?? null,
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'processes' => $processes,
                    'total_processes' => count($processes),
                    'running_processes' => count(array_filter($processes, fn($p) => $p['is_running'])),
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to list processes: {$e->getMessage()}",
            ];
        }
    }

    protected function diagnosticsTool(array $parameters): array
    {
        $paths = $parameters['paths'] ?? [];

        if (empty($paths)) {
            return ['success' => false, 'error' => 'paths parameter is required'];
        }

        try {
            $diagnostics = [];

            foreach ($paths as $path) {
                $resolvedPath = $this->resolvePath($path);

                if (!file_exists($resolvedPath)) {
                    $diagnostics[] = [
                        'file' => $path,
                        'type' => 'error',
                        'message' => 'File not found',
                        'line' => null,
                        'column' => null,
                    ];
                    continue;
                }

                // Get file diagnostics based on file type
                $fileDiagnostics = $this->analyzeFile($resolvedPath);
                $diagnostics = array_merge($diagnostics, $fileDiagnostics);
            }

            return [
                'success' => true,
                'data' => [
                    'diagnostics' => $diagnostics,
                    'total_issues' => count($diagnostics),
                    'errors' => count(array_filter($diagnostics, fn($d) => $d['type'] === 'error')),
                    'warnings' => count(array_filter($diagnostics, fn($d) => $d['type'] === 'warning')),
                    'info' => count(array_filter($diagnostics, fn($d) => $d['type'] === 'info')),
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to run diagnostics: {$e->getMessage()}",
            ];
        }
    }

    protected function rememberTool(array $parameters, $user = null): array
    {
        // Use first user if no user provided (for testing)
        if (!$user) {
            $user = \App\Models\User::first();
        }

        if (!$user) {
            return ['success' => false, 'error' => 'No user found for remember tool'];
        }

        $memory = $parameters['memory'] ?? '';
        $metadata = $parameters['metadata'] ?? [];

        if (!$memory) {
            return ['success' => false, 'error' => 'memory parameter is required'];
        }

        return $this->memoryService->remember($user, $memory, $metadata);
    }

    protected function renderMermaidTool(array $parameters): array
    {
        $diagramDefinition = $parameters['diagram_definition'] ?? '';
        $title = $parameters['title'] ?? 'Mermaid Diagram';
        $options = $parameters['options'] ?? [];

        if (!$diagramDefinition) {
            return ['success' => false, 'error' => 'diagram_definition parameter is required'];
        }

        return $this->mermaidService->renderDiagram($diagramDefinition, $title, $options);
    }

    protected function openBrowserTool(array $parameters): array
    {
        $url = $parameters['url'] ?? '';

        if (!$url) {
            return ['success' => false, 'error' => 'url parameter is required'];
        }

        try {
            // Validate URL
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                return ['success' => false, 'error' => 'Invalid URL format'];
            }

            // For security, only allow HTTP/HTTPS URLs
            $parsedUrl = parse_url($url);
            if (!in_array($parsedUrl['scheme'] ?? '', ['http', 'https'])) {
                return ['success' => false, 'error' => 'Only HTTP and HTTPS URLs are allowed'];
            }

            // Since we're in a server environment, we can't actually open a browser
            // Instead, we'll return the URL and instructions for the user
            return [
                'success' => true,
                'data' => [
                    'url' => $url,
                    'message' => 'URL ready to open in browser',
                    'instructions' => 'Please open the following URL in your browser: ' . $url,
                    'html_link' => "<a href=\"{$url}\" target=\"_blank\" rel=\"noopener noreferrer\">{$url}</a>",
                ],
                'action_required' => 'manual_browser_open',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to process URL: {$e->getMessage()}",
            ];
        }
    }

    protected function readTerminalTool(array $parameters): array
    {
        $onlySelected = $parameters['only_selected'] ?? false;

        try {
            // Since we're in a server environment, we can't read from an actual terminal
            // Instead, we'll provide information about the most recent process outputs
            $recentOutputs = [];

            foreach ($this->runningProcesses as $terminalId => $processInfo) {
                $process = $processInfo['process'];
                $output = $process->output();
                $errorOutput = $process->errorOutput();

                if (!empty($output) || !empty($errorOutput)) {
                    $recentOutputs[] = [
                        'terminal_id' => $terminalId,
                        'command' => $processInfo['command'],
                        'output' => $output,
                        'error_output' => $errorOutput,
                        'status' => $process->running() ? 'running' : 'completed',
                        'timestamp' => $processInfo['started_at']->toISOString(),
                    ];
                }
            }

            // Sort by most recent
            usort($recentOutputs, function ($a, $b) {
                return strtotime($b['timestamp']) - strtotime($a['timestamp']);
            });

            // If only_selected is true, return just the most recent
            if ($onlySelected && !empty($recentOutputs)) {
                $recentOutputs = [$recentOutputs[0]];
            }

            return [
                'success' => true,
                'data' => [
                    'terminal_outputs' => $recentOutputs,
                    'total_terminals' => count($this->runningProcesses),
                    'active_terminals' => count(array_filter($this->runningProcesses, fn($p) => $p['process']->running())),
                    'only_selected' => $onlySelected,
                    'note' => 'This shows output from managed processes. For actual terminal reading, use read-process with specific terminal_id.',
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to read terminal: {$e->getMessage()}",
            ];
        }
    }

    protected function webSearchTool(array $parameters): array
    {
        $query = $parameters['query'] ?? '';
        $numResults = $parameters['num_results'] ?? 5;

        if (!$query) {
            return ['success' => false, 'error' => 'Query parameter is required'];
        }

        $result = $this->webResearch->search($query, $numResults);

        return [
            'success' => $result['success'],
            'data' => $result,
            'error' => $result['error'] ?? null,
        ];
    }

    protected function webFetchTool(array $parameters): array
    {
        $url = $parameters['url'] ?? '';

        if (!$url) {
            return ['success' => false, 'error' => 'URL parameter is required'];
        }

        $result = $this->webResearch->fetchContent($url);

        return [
            'success' => $result['success'],
            'data' => $result,
            'error' => $result['error'] ?? null,
        ];
    }

    /**
     * Load running processes from cache.
     */
    protected function loadRunningProcesses(): void
    {
        $cached = Cache::get('tool_service_processes', []);
        $this->runningProcesses = $cached;

        // Clean up completed processes older than 1 hour
        $cutoff = now()->subHour();
        foreach ($this->runningProcesses as $id => $processInfo) {
            if (isset($processInfo['completed_at']) && $processInfo['completed_at'] < $cutoff) {
                unset($this->runningProcesses[$id]);
            }
        }

        $this->saveRunningProcesses();
    }

    /**
     * Save running processes to cache.
     */
    protected function saveRunningProcesses(): void
    {
        Cache::put('tool_service_processes', $this->runningProcesses, 3600); // 1 hour
    }

    /**
     * Analyze a file for potential issues.
     */
    protected function analyzeFile(string $filePath): array
    {
        $diagnostics = [];
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);

        switch ($extension) {
            case 'php':
                $diagnostics = array_merge($diagnostics, $this->analyzePHPFile($filePath, $lines));
                break;
            case 'js':
            case 'ts':
                $diagnostics = array_merge($diagnostics, $this->analyzeJavaScriptFile($filePath, $lines));
                break;
            case 'json':
                $diagnostics = array_merge($diagnostics, $this->analyzeJSONFile($filePath, $content));
                break;
            default:
                $diagnostics = array_merge($diagnostics, $this->analyzeGenericFile($filePath, $lines));
                break;
        }

        return $diagnostics;
    }

    /**
     * Analyze PHP file for syntax and common issues.
     */
    protected function analyzePHPFile(string $filePath, array $lines): array
    {
        $diagnostics = [];
        $relativePath = str_replace(base_path() . DIRECTORY_SEPARATOR, '', $filePath);

        // Check for PHP syntax errors
        $output = shell_exec("php -l " . escapeshellarg($filePath) . " 2>&1");
        if ($output && !str_contains($output, 'No syntax errors detected')) {
            if (preg_match('/on line (\d+)/', $output, $matches)) {
                $lineNumber = (int)$matches[1];
                $diagnostics[] = [
                    'file' => $relativePath,
                    'type' => 'error',
                    'message' => trim(str_replace($filePath, '', $output)),
                    'line' => $lineNumber,
                    'column' => null,
                ];
            }
        }

        // Check for common issues
        foreach ($lines as $lineNumber => $line) {
            $lineNum = $lineNumber + 1;

            // Check for missing semicolons (basic check)
            if (preg_match('/^\s*[^\/\*\s].*[^;{}]\s*$/', $line) && !str_contains($line, '//')) {
                if (!preg_match('/^\s*(if|else|while|for|foreach|switch|class|function|interface|trait|namespace|use)\s/', $line)) {
                    $diagnostics[] = [
                        'file' => $relativePath,
                        'type' => 'warning',
                        'message' => 'Possible missing semicolon',
                        'line' => $lineNum,
                        'column' => strlen(rtrim($line)),
                    ];
                }
            }

            // Check for TODO/FIXME comments
            if (preg_match('/(TODO|FIXME|HACK|XXX):\s*(.+)/', $line, $matches)) {
                $diagnostics[] = [
                    'file' => $relativePath,
                    'type' => 'info',
                    'message' => "{$matches[1]}: {$matches[2]}",
                    'line' => $lineNum,
                    'column' => strpos($line, $matches[1]),
                ];
            }
        }

        return $diagnostics;
    }

    /**
     * Analyze JavaScript/TypeScript file.
     */
    protected function analyzeJavaScriptFile(string $filePath, array $lines): array
    {
        $diagnostics = [];
        $relativePath = str_replace(base_path() . DIRECTORY_SEPARATOR, '', $filePath);

        foreach ($lines as $lineNumber => $line) {
            $lineNum = $lineNumber + 1;

            // Check for console.log statements
            if (preg_match('/console\.(log|warn|error|debug)/', $line)) {
                $diagnostics[] = [
                    'file' => $relativePath,
                    'type' => 'warning',
                    'message' => 'Console statement found - consider removing for production',
                    'line' => $lineNum,
                    'column' => strpos($line, 'console'),
                ];
            }

            // Check for TODO/FIXME comments
            if (preg_match('/(TODO|FIXME|HACK|XXX):\s*(.+)/', $line, $matches)) {
                $diagnostics[] = [
                    'file' => $relativePath,
                    'type' => 'info',
                    'message' => "{$matches[1]}: {$matches[2]}",
                    'line' => $lineNum,
                    'column' => strpos($line, $matches[1]),
                ];
            }
        }

        return $diagnostics;
    }

    /**
     * Analyze JSON file for syntax errors.
     */
    protected function analyzeJSONFile(string $filePath, string $content): array
    {
        $diagnostics = [];
        $relativePath = str_replace(base_path() . DIRECTORY_SEPARATOR, '', $filePath);

        json_decode($content);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $diagnostics[] = [
                'file' => $relativePath,
                'type' => 'error',
                'message' => 'JSON syntax error: ' . json_last_error_msg(),
                'line' => null,
                'column' => null,
            ];
        }

        return $diagnostics;
    }

    /**
     * Analyze generic file for common issues.
     */
    protected function analyzeGenericFile(string $filePath, array $lines): array
    {
        $diagnostics = [];
        $relativePath = str_replace(base_path() . DIRECTORY_SEPARATOR, '', $filePath);

        foreach ($lines as $lineNumber => $line) {
            $lineNum = $lineNumber + 1;

            // Check for TODO/FIXME comments
            if (preg_match('/(TODO|FIXME|HACK|XXX):\s*(.+)/', $line, $matches)) {
                $diagnostics[] = [
                    'file' => $relativePath,
                    'type' => 'info',
                    'message' => "{$matches[1]}: {$matches[2]}",
                    'line' => $lineNum,
                    'column' => strpos($line, $matches[1]),
                ];
            }

            // Check for very long lines
            if (strlen($line) > 120) {
                $diagnostics[] = [
                    'file' => $relativePath,
                    'type' => 'warning',
                    'message' => 'Line too long (>120 characters)',
                    'line' => $lineNum,
                    'column' => 120,
                ];
            }
        }

        return $diagnostics;
    }

    /**
     * Clean up old processes.
     */
    public function cleanupProcesses(): void
    {
        $cutoff = now()->subHours(2);
        $cleaned = 0;

        foreach ($this->runningProcesses as $id => $processInfo) {
            $process = $processInfo['process'];

            // Kill processes running for more than 2 hours
            if ($process->running() && $processInfo['started_at'] < $cutoff) {
                try {
                    $process->stop();
                    $this->runningProcesses[$id]['status'] = 'timeout_killed';
                    $this->runningProcesses[$id]['killed_at'] = now();
                    $cleaned++;
                } catch (\Exception $e) {
                    Log::warning("Failed to kill long-running process {$id}: {$e->getMessage()}");
                }
            }
        }

        if ($cleaned > 0) {
            Log::info("Cleaned up {$cleaned} long-running processes");
            $this->saveRunningProcesses();
        }
    }
}
